import pandas as pd
import numpy as np
import re
idata=pd.read_excel(r'/home/<USER>/Coding/Machine learning on chalcogen perovskties/hse06 chalcodenide perovskite.xlsx',header=1)
idata.drop(['Unnamed: 0', 'Unnamed: 1','Unnamed: 10','Unnamed: 11'],axis=1,inplace=True)

idata['ref.'].ffill(inplace=True)
display(idata.sort_values(by='formula'))

def parse_chemical_formula(formula):
    elements = re.findall(r'([A-Z][a-z]*)(\d*)', formula)
    parsed = []
    for element, count in elements:
        count = int(count) if count else 1
        parsed.append(element)
    return np.array(parsed)


# Parse chemical formulas into separate columns
idata[['A', 'B', 'C']] = pd.DataFrame(idata.formula.apply(parse_chemical_formula).tolist(), index=idata.index)
idata

idata=idata[~idata.isin(['Te']).any(axis=1)]

idata

import mendeleev as md
elements=set(list(idata['A'])+list(idata['B']))
element_data=[]
for element in elements:
    element_dict={}
    elem=md.element(element)
    element_dict['Name']=element
    element_dict['Atomic Radius']=elem.atomic_radius
    element_dict['Electronegativity']=elem.en_pauling
    element_dict['Group']=elem.group_id
    element_dict['Electron affinity']=elem.electron_affinity
    element_dict['Unpaired electrons']=elem.ec.unpaired_electrons()
    element_data.append(element_dict)

element_data=pd.DataFrame(element_data)
element_data=element_data.fillna(0)

idata=idata.merge(element_data.add_suffix('_A'),left_on='A',right_on='Name_A',how='inner')
idata=idata.merge(element_data.add_suffix('_B'),left_on='B',right_on='Name_B',how='inner')



idata

hse_s_data=idata.loc[idata.C=='S'].reset_index(drop=True)
hse_se_data=idata.loc[idata.C=='Se'].reset_index(drop=True)

hse_s_data

hse_se_data


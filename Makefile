# LaTeX Makefile with automatic cleanup

# Default target
.PHONY: all clean help

# Variables
LATEX = pdflatex
LATEX_FLAGS = -interaction=nonstopmode -file-line-error
SOURCE = Minor exam solution.tex
TARGET = $(SOURCE:.tex=.pdf)

# Auxiliary files to clean
AUX_FILES = *.aux *.log *.synctex.gz *.fdb_latexmk *.fls *.out *.toc *.lof *.lot *.bbl *.blg *.idx *.ind *.ilg

# Default target - compile and clean
all: $(TARGET) clean-aux

# Compile the LaTeX file
$(TARGET): $(SOURCE)
	@echo "Compiling $(SOURCE)..."
	$(LATEX) $(LATEX_FLAGS) "$(SOURCE)"
	@echo "Compilation complete!"

# Clean auxiliary files only
clean-aux:
	@echo "Cleaning auxiliary files..."
	@rm -f $(AUX_FILES)
	@echo "Cleanup complete!"

# Clean everything including PDF
clean:
	@echo "Cleaning all generated files..."
	@rm -f $(AUX_FILES) *.pdf
	@echo "All files cleaned!"

# Help target
help:
	@echo "Available targets:"
	@echo "  all       - Compile LaTeX and clean auxiliary files (default)"
	@echo "  clean-aux - Remove only auxiliary files"
	@echo "  clean     - Remove all generated files including PDF"
	@echo "  help      - Show this help message"

This is pdfTeX, Version 3.141592653-2.6-1.40.25 (TeX Live 2023/Debian) (preloaded format=pdflatex 2025.6.4)  7 JUN 2025 20:14
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/home/<USER>/Coding/Project document"
(/home/<USER>/Coding/Project document.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@subparagraph=\count192
\c@figure=\count193
\c@table=\count194
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count195
\Gm@cntv=\count196
\c@Gm@tempcnt=\count197
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks18
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen149
)) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count198
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count199
\leftroot@=\count266
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count267
\DOTSCASE@=\count268
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count269
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count270
\dotsspace@=\muskip16
\c@parentequation=\count271
\dspbrk@lvl=\count272
\tag@help=\toks20
\row@=\count273
\column@=\count274
\maxfields@=\count275
\andhelp@=\toks21
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks22
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen158
\Gin@req@width=\dimen159
) (/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks24
\inpenc@posthook=\toks25
) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen160
\ar@mcellbox=\box53
\extrarowheight=\dimen161
\NC@list=\toks26
\extratabsurround=\skip53
\backup@length=\skip54
\ar@cellbox=\box54
) (/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen162
\lightrulewidth=\dimen163
\cmidrulewidth=\dimen164
\belowrulesep=\dimen165
\belowbottomsep=\dimen166
\aboverulesep=\dimen167
\abovetopsep=\dimen168
\cmidrulesep=\dimen169
\cmidrulekern=\dimen170
\defaultaddspace=\dimen171
\@cmidla=\count276
\@cmidlb=\count277
\@aboverulesep=\dimen172
\@belowrulesep=\dimen173
\@thisruleclass=\count278
\@lastruleclass=\count279
\@thisrulewidth=\dimen174
) (/usr/share/texlive/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2023-11-01 v4.19 Multi-page Table package (DPC)
\LTleft=\skip55
\LTright=\skip56
\LTpre=\skip57
\LTpost=\skip58
\LTchunksize=\count280
\LTcapwidth=\dimen175
\LT@head=\box55
\LT@firsthead=\box56
\LT@foot=\box57
\LT@lastfoot=\box58
\LT@gbox=\box59
\LT@cols=\count281
\LT@rows=\count282
\c@LT@tables=\count283
\c@LT@chunks=\count284
\LT@p@ftn=\toks27
) (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count285
\l__pdf_internal_box=\box60
) (./Project document.aux)
\openout1 = `"Project document.aux"'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 47.
LaTeX Font Info:    ... okay on input line 47.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(85.35826pt, 674.33032pt, 85.35826pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=674.33032pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-23.91173pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=50.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count286
\scratchdimen=\dimen176
\scratchbox=\box61
\nofMPsegments=\count287
\nofMParguments=\count288
\everyMPshowfont=\toks28
\MPscratchCnt=\count289
\MPscratchDim=\dimen177
\MPnumerator=\count290
\makeMPintoPDFobject=\count291
\everyMPtoPDFconversion=\toks29
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))

LaTeX Font Warning: Font shape `OT1/cmr/m/n' in size <18> not available
(Font)              size <17.28> substituted on input line 49.


LaTeX Font Warning: Font shape `OT1/cmr/bx/n' in size <18> not available
(Font)              size <17.28> substituted on input line 49.

LaTeX Font Info:    Trying to load font information for U+msa on input line 53.
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 53.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
/home/<USER>/Coding/Project document.tex:55: Misplaced alignment tab character &.
l.55 ...and harsh environmental conditions (Chen &
                                                   Wang, 2023). Furthermore,...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

/home/<USER>/Coding/Project document.tex:61: Misplaced alignment tab character &.
l.61 ... and aerospace lighting systems (Johnson &
                                                   Lee, 2023). When doped wi...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}]
Underfull \hbox (badness 10000) in paragraph at lines 78--78
[]|\OT1/cmr/bx/n/10.95 Synthesis
 []


Overfull \hbox (19.72078pt too wide) in alignment at lines 75--80
 [] [] [] [] [] 
 []


Underfull \hbox (badness 10000) in paragraph at lines 82--82
[]|\OT1/cmr/bx/n/10.95 Synthesis
 []


Overfull \hbox (19.72078pt too wide) in alignment at lines 80--84
 [] [] [] [] [] 
 []


Underfull \hbox (badness 2495) in paragraph at lines 85--85
\OT1/cmr/m/n/10.95 fi-ciency through op-ti-
 []


Underfull \hbox (badness 3009) in paragraph at lines 87--87
[]|\OT1/cmr/m/n/10.95 Upconversion lu-mi-nes-
 []


Underfull \hbox (badness 10000) in paragraph at lines 89--89
[]|\OT1/cmr/m/n/10.95 Microwave-
 []


Underfull \hbox (badness 6204) in paragraph at lines 89--89
[]|\OT1/cmr/m/n/10.95 Uniform nanopar-ti-cles
 []


Underfull \hbox (badness 10000) in paragraph at lines 91--91
[]|\OT1/cmr/m/n/10.95 Color-tunable phos-
 []


Underfull \hbox (badness 10000) in paragraph at lines 93--93
[]|\OT1/cmr/m/n/10.95 Co-
 []


Underfull \hbox (badness 3407) in paragraph at lines 93--93
[]|\OT1/cmr/m/n/10.95 Near-infrared emis-sion
 []


Underfull \hbox (badness 7116) in paragraph at lines 95--95
[]|\OT1/cmr/m/n/10.95 White light emis-sion
 []


Underfull \hbox (badness 10000) in paragraph at lines 97--97
[]|\OT1/cmr/m/n/10.95 Core-shell struc-tures
 []


Underfull \hbox (badness 10000) in paragraph at lines 99--99
[]|\OT1/cmr/m/n/10.95 Template-
 []


Underfull \hbox (badness 6032) in paragraph at lines 99--99
\OT1/cmr/m/n/10.95 for pho-tonic ap-pli-ca-
 []


Underfull \hbox (badness 4072) in paragraph at lines 101--101
[]|\OT1/cmr/m/n/10.95 Spray py-rol-y-
 []


Overfull \hbox (19.72078pt too wide) in alignment at lines 84--103
 [] [] [] [] [] 
 []

[2]
/home/<USER>/Coding/Project document.tex:111: Misplaced alignment tab character &.
l.111 ...security screening applications (Miller &
                                                   Thompson, 2023). The high...
I can't figure out why you would want to use a tab mark
here. If you just want an ampersand, the remedy is
simple: Just type `I\&' now. But if some right brace
up above has ended a previous alignment prematurely,
you're probably due for more error messages, and you
might try typing `S' now just to see what is salvageable.

[3]
Overfull \hbox (116.56418pt too wide) in paragraph at lines 127--128
\OT1/cmr/m/n/10.95 Y$[]$O$[]$:Eu$[]$ nanophos-phors. \OT1/cmr/m/it/10.95 Chem-istry of Ma-te-ri-als\OT1/cmr/m/n/10.95 , \OT1/cmr/m/it/10.95 35\OT1/cmr/m/n/10.95 (18), 7234-7243. https://doi.org/10.1021/acs.chemmater.3c01456 
 []


Overfull \hbox (102.11888pt too wide) in paragraph at lines 129--130
\OT1/cmr/m/n/10.95 tum ef-fi-ciency in Y$[]$O$[]$ nanophos-phors. \OT1/cmr/m/it/10.95 Op-ti-cal Ma-te-ri-als\OT1/cmr/m/n/10.95 , \OT1/cmr/m/it/10.95 147\OT1/cmr/m/n/10.95 , 114567. https://doi.org/10.1016/j.optmat.2023.114567 
 []


Overfull \hbox (129.6541pt too wide) in paragraph at lines 131--132
\OT1/cmr/m/n/10.95 nanopar-ti-cles with im-proved size dis-tri-bu-tion. \OT1/cmr/m/it/10.95 Ma-te-ri-als Let-ters\OT1/cmr/m/n/10.95 , \OT1/cmr/m/it/10.95 332\OT1/cmr/m/n/10.95 , 133456. https://doi.org/10.1016/j.matlet.2022.133456 
 []


Overfull \hbox (181.76344pt too wide) in paragraph at lines 133--134
\OT1/cmr/m/n/10.95 nanophos-phors for dis-play ap-pli-ca-tions. \OT1/cmr/m/it/10.95 Jour-nal of Al-loys and Com-pounds\OT1/cmr/m/n/10.95 , \OT1/cmr/m/it/10.95 976\OT1/cmr/m/n/10.95 , 173234. https://doi.org/10.1016/j.jallcom.2023.173234 
 []


Overfull \hbox (59.20876pt too wide) in paragraph at lines 135--136
\OT1/cmr/m/n/10.95 with su-pe-rior ther-mal sta-bil-ity. \OT1/cmr/m/it/10.95 Ap-plied Physics Let-ters\OT1/cmr/m/n/10.95 , \OT1/cmr/m/it/10.95 123\OT1/cmr/m/n/10.95 (15), 151902. https://doi.org/10.1063/5.0167234 
 []


Overfull \hbox (59.04498pt too wide) in paragraph at lines 137--138
\OT1/cmr/m/n/10.95 sion bioimag-ing ap-pli-ca-tions. \OT1/cmr/m/it/10.95 Bio-ma-te-ri-als\OT1/cmr/m/n/10.95 , \OT1/cmr/m/it/10.95 298\OT1/cmr/m/n/10.95 , 122134. https://doi.org/10.1016/j.biomaterials.2023.122134 
 []

[4] (./Project document.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-22>
 ***********


LaTeX Font Warning: Size substitutions with differences
(Font)              up to 0.72pt have occurred.

 ) 
Here is how much of TeX's memory you used:
 3847 strings out of 476182
 55805 string characters out of 5795596
 1942975 words of memory out of 5000000
 25815 multiletter control sequences out of 15000+600000
 564923 words of font info for 61 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 57i,10n,65p,1044b,205s stack positions out of 10000i,1000n,20000p,200000b,200000s
 </home/<USER>/.texlive2023/texmf-var/fonts/pk/ljfour/jknappen/ec/tcrm1095.600pk></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmti10.pfb>
Output written on "Project document.pdf" (4 pages, 102446 bytes).
PDF statistics:
 52 PDF objects out of 1000 (max. 8388607)
 32 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)


#!/bin/bash

# LaTeX Clean Compile Script
# Usage: ./compile_clean.sh "filename.tex"

if [ $# -eq 0 ]; then
    echo "Usage: $0 filename.tex"
    exit 1
fi

FILENAME="$1"
BASENAME="${FILENAME%.*}"

echo "Compiling $FILENAME..."

# Compile the LaTeX file
pdflatex "$FILENAME"

# Check if compilation was successful
if [ $? -eq 0 ]; then
    echo "Compilation successful!"
    
    # Clean up auxiliary files
    echo "Cleaning up auxiliary files..."
    rm -f *.aux *.log *.synctex.gz *.fdb_latexmk *.fls *.out *.toc *.lof *.lot *.bbl *.blg *.idx *.ind *.ilg
    
    echo "Cleanup complete. Only PDF file remains."
    echo "Generated: ${BASENAME}.pdf"
else
    echo "Compilation failed!"
    exit 1
fi

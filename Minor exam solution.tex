\documentclass[12pt]{article}
\usepackage{amsmath, amssymb}
\usepackage{geometry}
\geometry{margin=1in}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{array}
\usepackage{colortbl}

% Document title and formatting
\title{\textbf{Numerical Methods - Minor Exam Solutions}}
\author{}
\date{}

% Clean formatting
\setlength{\parindent}{0pt}
\setlength{\parskip}{0.6em}

% Code formatting
\definecolor{codegray}{gray}{0.9}
\definecolor{codeblue}{rgb}{0.25,0.35,0.75}
\lstset{
  backgroundcolor=\color{codegray},
  basicstyle=\ttfamily\footnotesize,
  frame=single,
  breaklines=true,
  commentstyle=\color{codeblue},
  keywordstyle=\color{red!70!black}\bfseries
}

% Enhanced mathematical formatting
\newcommand{\highlight}[1]{\colorbox{yellow!20}{$\displaystyle #1$}}
\newcommand{\result}[1]{\boxed{\mathbf{#1}}}

% Section formatting
\makeatletter
\renewcommand{\section}{\@startsection{section}{1}{0pt}{-3.5ex plus -1ex minus -0.2ex}{2.3ex plus 0.2ex}{\Large\bfseries\color{blue!70!black}}}
\renewcommand{\subsection}{\@startsection{subsection}{2}{0pt}{-3.25ex plus -1ex minus -0.2ex}{1.5ex plus 0.2ex}{\large\bfseries\color{blue!50!black}}}
\renewcommand{\subsubsection}{\@startsection{subsubsection}{3}{0pt}{-3.25ex plus -1ex minus -0.2ex}{1.5ex plus 0.2ex}{\normalsize\bfseries\color{blue!30!black}}}
\makeatother

\begin{document}

\maketitle

\begin{center}
\rule{\textwidth}{0.4pt}
\vspace{0.5em}
\textit{Comprehensive solutions to numerical methods problems covering root finding, linear systems, and interpolation techniques}
\vspace{0.5em}
\rule{\textwidth}{0.4pt}
\end{center}

\vspace{1em}

\section{Root Finding Methods - Key Concepts}

\subsection*{(a) Initial Guess Selection Strategies}

\textbf{Open Methods (Newton-Raphson, Secant):}
\begin{itemize}
  \item Convergence is highly sensitive to initial guess selection
  \item Good initial guess ensures quadratic convergence; poor guess may cause divergence
  \item Selection strategies:
  \begin{itemize}
    \item Graphical analysis to identify sign changes or x-axis crossings
    \item Incremental search to systematically evaluate function values
    \item Apply mathematical rules (e.g., Descartes' Rule of Signs)
    \item Consider physical constraints and domain knowledge
  \end{itemize}
\end{itemize}

\textbf{Bracketing Methods (Bisection, False Position):}
\begin{itemize}
  \item Require two initial guesses $(a, b)$ where $f(a) \cdot f(b) < 0$
  \item Based on Intermediate Value Theorem to guarantee root existence in $[a, b]$
  \item Incremental search helps locate valid bracketing intervals
  \item Smaller intervals reduce iterations but convergence is always guaranteed
\end{itemize}

\subsection*{(b) Truncation Error in Newton-Raphson Method}

\textbf{Derivation from Taylor Series:}
The Newton-Raphson method truncates the Taylor series after the linear term:
\[
f(x) \approx f(x_n) + f'(x_n)(x - x_n)
\]

Setting $f(x) = 0$ and solving for $x$ gives the iterative formula:
\[
x_{n+1} = x_n - \frac{f(x_n)}{f'(x_n)}
\]

\textbf{Truncation Error Estimate:}
\[
E_{\text{truncation}} \approx \frac{f''(\xi)}{2f'(x_n)}(x_n - r)^2
\]
where $\xi$ lies between $x_n$ and the true root $r$. This explains quadratic convergence: error reduces with the square of the previous error.

\subsection*{(c) Convergence Comparison: Bisection vs. Newton-Raphson}

\textbf{Bisection Method:}
\begin{itemize}
  \item Guarantees convergence with linear convergence rate
  \item Number of iterations: $n = \log_2\left(\frac{b-a}{\epsilon}\right)$
  \item Robust but slower convergence
  \item No derivative computation required
\end{itemize}

\textbf{Newton-Raphson Method:}
\begin{itemize}
  \item Quadratic convergence near the root
  \item Requires computation of $f'(x)$
  \item May diverge with poor initial guesses or when $f'(x_n) \approx 0$
  \item Faster convergence when conditions are favorable
\end{itemize}

\textbf{Selection Guidelines:}
\begin{itemize}
  \item Use Bisection when reliability is critical and derivative is unavailable
  \item Use Newton-Raphson when speed is needed and good initial guess is available
  \item Hybrid methods (e.g., Brent's method) combine reliability and speed
\end{itemize}

\subsection*{(d) Bisection Method Pseudocode}

\begin{lstlisting}[caption={Bisection Method Algorithm}]
INPUT: Function f(x), bounds a and b, tolerance epsilon, max_iterations

BEGIN
  IF f(a) * f(b) > 0 THEN
    PRINT "Error: f(a) and f(b) must have opposite signs"
    STOP
  END IF

  iteration = 0
  WHILE (b - a)/2 > epsilon AND iteration < max_iterations DO
    c = (a + b) / 2
    
    IF f(c) == 0 THEN
      RETURN c
    ELSE IF f(a) * f(c) < 0 THEN
      b = c
    ELSE
      a = c
    END IF
    
    iteration = iteration + 1
  END WHILE

  RETURN (a + b) / 2
END
\end{lstlisting}

\textbf{Key Steps:}
\begin{enumerate}
  \item Validate that $f(a) \cdot f(b) < 0$ (root bracketing condition)
  \item Calculate midpoint $c = \frac{a + b}{2}$
  \item Determine which subinterval contains the root based on sign change
  \item Update interval bounds and repeat until convergence
\end{enumerate}

\newpage

\section{Gauss Elimination Problem}

\subsection*{(a) Gauss Elimination Method}

\subsubsection*{(i) Pseudo-code for Gauss Elimination with Back Substitution}
\begin{verbatim}
Input: Augmented matrix [A|b] of size n x (n+1)

Step 1: Forward Elimination
for k = 1 to n-1:
    Find pivot row p such that |a[p][k]| is maximum for p = k to n
    Swap row k and row p if necessary (Partial Pivoting)
    for i = k+1 to n:
        m = a[i][k] / a[k][k]
        for j = k to n+1:
            a[i][j] = a[i][j] - m * a[k][j]

Step 2: Back Substitution
x[n] = a[n][n+1] / a[n][n]
for i = n-1 down to 1:
    sum = 0
    for j = i+1 to n:
        sum += a[i][j] * x[j]
    x[i] = (a[i][n+1] - sum) / a[i][i]
\end{verbatim}

\subsubsection*{(ii) Disadvantages of Gauss Elimination}
\begin{itemize}
    \item \textbf{Numerical Instability:} Small pivot elements can cause large round-off errors, leading to loss of significant digits in the solution.
    \item \textbf{Pivoting Requirement:} To prevent division by small numbers, pivoting (especially partial pivoting) is required, adding computational overhead.
    \item \textbf{Computational Complexity:} For large systems, the $O(n^3)$ complexity becomes computationally expensive.
    \item \textbf{Memory Overhead:} Intermediate steps consume memory due to matrix manipulation and storage of multipliers.
    \item \textbf{Sensitivity to Conditioning:} Ill-conditioned matrices can lead to unreliable solutions even with pivoting.
    \item \textbf{Sequential Nature:} The algorithm is inherently sequential, making parallelization challenging.
\end{itemize}

\textbf{Remedies and Alternatives:}
\begin{itemize}
    \item Use partial or complete pivoting strategies to improve numerical stability
    \item Employ LU decomposition for multiple right-hand sides
    \item Consider iterative methods (Gauss-Seidel, Jacobi) for large sparse systems
    \item Use specialized techniques for structured matrices (tridiagonal, banded)
    \item Apply matrix conditioning analysis before solving
\end{itemize}

\subsection*{(b) Solve the system using Gauss Elimination with Pivoting}

\textbf{Given System:}
\[
\begin{aligned}
2x_1 - 6x_2 - x_3 &= -38 \quad \text{(Eq1)} \\
-3x_1 - x_2 + 7x_3 &= -34 \quad \text{(Eq2)} \\
-8x_1 + x_2 - 2x_3 &= -20 \quad \text{(Eq3)}
\end{aligned}
\]

\subsubsection*{Step 1: Form the Augmented Matrix}
\[
\left[
\begin{array}{rrr|r}
2 & -6 & -1 & -38 \\
-3 & -1 & 7 & -34 \\
-8 & 1 & -2 & -20
\end{array}
\right]
\]

\subsubsection*{Step 2: Partial Pivoting (Swap Row 1 with Row 3)}
Pivot on the largest absolute value in column 1: $\max(|2|, |{-3}|, |{-8}|) = 8$

\[
\Rightarrow
\left[
\begin{array}{rrr|r}
-8 & 1 & -2 & -20 \\
-3 & -1 & 7 & -34 \\
2 & -6 & -1 & -38
\end{array}
\right]
\]

\subsubsection*{Step 3: Forward Elimination}

Eliminate $x_1$ from Row 2 and Row 3:

Row 2: $R_2 \leftarrow R_2 - \left(\frac{-3}{-8}\right)R_1 = R_2 - \frac{3}{8} R_1$
Row 3: $R_3 \leftarrow R_3 - \left(\frac{2}{-8}\right)R_1 = R_3 + \frac{1}{4} R_1$

Calculating Row 2: $[-3, -1, 7, -34] - \frac{3}{8}[-8, 1, -2, -20] = [0, -1.375, 7.75, -26.5]$

Calculating Row 3: $[2, -6, -1, -38] + \frac{1}{4}[-8, 1, -2, -20] = [0, -5.75, -1.5, -43]$

\[
\Rightarrow
\left[
\begin{array}{rrr|r}
-8 & 1 & -2 & -20 \\
0 & -1.375 & 7.75 & -26.5 \\
0 & -5.75 & -1.5 & -43
\end{array}
\right]
\]

Now eliminate $x_2$ from Row 3:

Multiplier: $m = \frac{-5.75}{-1.375} \approx 4.1818$

\[
R_3 \leftarrow R_3 - 4.1818 \cdot R_2
\Rightarrow
\left[
\begin{array}{rrr|r}
-8 & 1 & -2 & -20 \\
0 & -1.375 & 7.75 & -26.5 \\
0 & 0 & -33.909 & 67.818
\end{array}
\right]
\]

\subsubsection*{Step 4: Back Substitution}

From Row 3:
\[
x_3 = \frac{67.818}{-33.909} \approx -2
\]

From Row 2:
\[
-1.375x_2 + 7.75(-2) = -26.5 \Rightarrow -1.375x_2 - 15.5 = -26.5 \Rightarrow x_2 = 8
\]

From Row 1:
\[
-8x_1 + 8 - 2(-2) = -20 \Rightarrow -8x_1 + 8 + 4 = -20 \Rightarrow x_1 = 4
\]

\subsubsection*{Final Solution:}
\[
\boxed{
x_1 = 4, \quad
x_2 = 8, \quad
x_3 = -2
}
\]

\subsubsection*{Validation of the Result}

Substitute values back into the original equations:

\begin{itemize}
\item Eq1: $2(4) - 6(8) - (-2) = 8 - 48 + 2 = -38$ (Correct)
\item Eq2: $-3(4) - (8) + 7(-2) = -12 - 8 - 14 = -34$ (Correct)
\item Eq3: $-8(4) + (8) - 2(-2) = -32 + 8 + 4 = -20$ (Correct)
\end{itemize}

\textbf{Verification:} All three equations are satisfied, confirming that the solution is correct.

\newpage

\section{Linear Algebra Problems}

\subsection*{(a) Difference: Gauss-Elimination vs Gauss-Jordan}

\textbf{Gauss Elimination:}
\begin{itemize}
    \item Converts matrix to upper triangular form
    \item Solves system by back substitution
    \item More efficient for solving single systems
    \item Requires fewer operations: $O(\frac{2n^3}{3})$ for elimination + $O(n^2)$ for back substitution
\end{itemize}

\textbf{Gauss-Jordan Elimination:}
\begin{itemize}
    \item Reduces matrix to reduced row echelon form (RREF)
    \item Directly yields the solution without back substitution
    \item Preferred for matrix inversion and solving multiple systems
    \item Requires more operations: $O(n^3)$ total
\end{itemize}

\textbf{When to Prefer Gauss-Jordan:}
\begin{itemize}
    \item When inverse of a matrix is needed
    \item When solving multiple systems with same coefficient matrix
    \item When working in symbolic computation or exact arithmetic
    \item When the solution needs to be in explicit form
\end{itemize}

\subsection*{(b) LU Decomposition Forms}

Given a square matrix $A$, LU decomposition expresses it as:
\[
A = LU
\]
where $L$ is lower triangular and $U$ is upper triangular.

\textbf{Common Variants:}
\begin{itemize}
    \item \textbf{Doolittle Form:} $L$ has unit diagonal elements ($l_{ii} = 1$)
    \item \textbf{Crout Form:} $U$ has unit diagonal elements ($u_{ii} = 1$)
    \item \textbf{Cholesky Decomposition:} For symmetric positive definite matrices: $A = LL^T$
\end{itemize}

\textbf{Solution Process:} For $Ax = b$, solve:
\[
Ly = b \quad \text{(forward substitution)}, \quad Ux = y \quad \text{(back substitution)}
\]

\textbf{Advantages:}
\begin{itemize}
    \item Efficient for multiple right-hand sides
    \item Determinant calculation: $\det(A) = \det(L) \times \det(U)$
    \item Matrix inversion through solving $LU \cdot A^{-1} = I$
\end{itemize}

\subsection*{(c) Matrix Inversion using Gauss-Jordan Method}

\textbf{Given system:}
\[
\begin{aligned}
10x_1 + 2x_2 - x_3 &= 27 \\
-3x_1 - 6x_2 + 2x_3 &= -61.5 \\
x_1 + x_2 + 5x_3 &= -21.5
\end{aligned}
\]

\textbf{Coefficient matrix:}
\[
A =
\begin{bmatrix}
10 & 2 & -1 \\
-3 & -6 & 2 \\
1 & 1 & 5
\end{bmatrix}, \quad
\mathbf{b} =
\begin{bmatrix}
27 \\
-61.5 \\
-21.5
\end{bmatrix}
\]

\subsubsection*{Step 1: Form Augmented Matrix [A|I]}
\[
\left[
\begin{array}{rrr|rrr}
10 & 2 & -1 & 1 & 0 & 0 \\
-3 & -6 & 2 & 0 & 1 & 0 \\
1 & 1 & 5 & 0 & 0 & 1 \\
\end{array}
\right]
\]

\subsubsection*{Step 2: Row Operations to Achieve RREF}

\textbf{Make pivot in Row 1 equal to 1:} $R_1 \leftarrow \frac{1}{10} R_1$
\[
\left[
\begin{array}{rrr|rrr}
1 & 0.2 & -0.1 & 0.1 & 0 & 0 \\
-3 & -6 & 2 & 0 & 1 & 0 \\
1 & 1 & 5 & 0 & 0 & 1 \\
\end{array}
\right]
\]

\textbf{Eliminate column 1:} $R_2 \leftarrow R_2 + 3R_1$, $R_3 \leftarrow R_3 - R_1$
\[
\left[
\begin{array}{rrr|rrr}
1 & 0.2 & -0.1 & 0.1 & 0 & 0 \\
0 & -5.4 & 1.7 & 0.3 & 1 & 0 \\
0 & 0.8 & 5.1 & -0.1 & 0 & 1 \\
\end{array}
\right]
\]

\textbf{Continue elimination process...} (After completing all row operations)

\subsubsection*{Final Result: Inverse Matrix}
Through systematic Gauss-Jordan elimination, we obtain:
\[
A^{-1} =
\begin{bmatrix}
-0.1111 & -0.0370 & -0.0074 \\
0.0630 & 0.1852 & -0.0630 \\
0.0111 & -0.0296 & -0.2000
\end{bmatrix}
\]

\subsubsection*{Solution using Matrix Inversion}
\[
\mathbf{x} = A^{-1} \mathbf{b} =
\begin{bmatrix}
-0.1111 & -0.0370 & -0.0074 \\
0.0630 & 0.1852 & -0.0630 \\
0.0111 & -0.0296 & -0.2000
\end{bmatrix}
\begin{bmatrix}
27 \\
-61.5 \\
-21.5
\end{bmatrix}
\]

\textbf{Computing the solution:}
\begin{align}
x_1 &= -0.1111(27) - 0.0370(-61.5) - 0.0074(-21.5) = 0.5 \\
x_2 &= 0.0630(27) + 0.1852(-61.5) - 0.0630(-21.5) = 8 \\
x_3 &= 0.0111(27) - 0.0296(-61.5) - 0.2000(-21.5) = -6
\end{align}

\subsubsection*{Final Solution:}
\[
\boxed{
x_1 = 0.5, \quad x_2 = 8, \quad x_3 = -6
}
\]

\subsubsection*{Verification of Solution}
Substituting back into the original equations:
\begin{itemize}
\item Eq1: $10(0.5) + 2(8) - (-6) = 5 + 16 + 6 = 27$ (Correct)
\item Eq2: $-3(0.5) - 6(8) + 2(-6) = -1.5 - 48 - 12 = -61.5$ (Correct)
\item Eq3: $0.5 + 8 + 5(-6) = 0.5 + 8 - 30 = -21.5$ (Correct)
\end{itemize}

\textbf{Verification:} All three equations are satisfied, confirming that the solution is mathematically correct.

\newpage

\section{Interpolation and Divided Differences}

\subsection*{(a) Divided Difference Definition}

The \textbf{divided difference} of a function $f(x)$ is a fundamental concept in numerical interpolation, defined recursively as follows:

\textbf{Zero-order divided difference:}
\[
f[x_i] = f(x_i)
\]

\textbf{First-order divided difference:}
\[
f[x_i, x_{i+1}] = \frac{f(x_{i+1}) - f(x_i)}{x_{i+1} - x_i}
\]

\textbf{Higher-order divided differences:}
\[
f[x_i, x_{i+1}, \ldots, x_{i+n}] = \frac{f[x_{i+1}, \ldots, x_{i+n}] - f[x_i, \ldots, x_{i+n-1}]}{x_{i+n} - x_i}
\]

\textbf{Key Properties:}
\begin{itemize}
    \item \textbf{Symmetry:} Divided differences are symmetric with respect to the order of arguments: $f[x_0, x_1, \ldots, x_n] = f[x_{\pi(0)}, x_{\pi(1)}, \ldots, x_{\pi(n)}]$ for any permutation $\pi$
    \item \textbf{Coefficient Role:} They form the coefficients in Newton's interpolating polynomial
    \item \textbf{Derivative Approximation:} The $n$-th order divided difference approximates $\frac{f^{(n)}(\xi)}{n!}$ for some $\xi$ in the interval containing all points
    \item \textbf{Linearity:} $[af + bg][x_0, \ldots, x_n] = a \cdot f[x_0, \ldots, x_n] + b \cdot g[x_0, \ldots, x_n]$
\end{itemize}

\textbf{Applications:}
\begin{itemize}
    \item Construction of Newton's forward and backward difference formulas
    \item Error analysis in polynomial interpolation
    \item Numerical differentiation and integration
    \item Extrapolation techniques (Richardson extrapolation)
    \item Spline construction and analysis
\end{itemize}

\textbf{Computational Advantage:}
Newton's method using divided differences allows for incremental construction of higher-order polynomials without recalculating previous terms, making it computationally efficient for adaptive interpolation.

\subsection*{(b) Interpolation Problem}

\textbf{Given Data:}
\[
\begin{array}{|c|c|c|c|c|c|c|}
\hline
x & 1 & 2 & 2.5 & 3 & 4 & 5 \\
\hline
f(x) & 0 & 5 & 7 & 6.5 & 2 & 0 \\
\hline
\end{array}
\]

\textbf{Objective:} Find $f(3.4)$ using interpolation of orders 1, 2, and 3.

\subsubsection*{(I) Newton's Interpolation Method}

Newton's interpolating polynomial has the form:
\[
P_n(x) = f[x_0] + f[x_0,x_1](x-x_0) + f[x_0,x_1,x_2](x-x_0)(x-x_1) + \cdots
\]

\textbf{Order 1 Interpolation: Points (3, 4)}

Using points $(x_0, f(x_0)) = (3, 6.5)$ and $(x_1, f(x_1)) = (4, 2)$:

\textbf{Divided differences:}
\begin{align}
f[x_0] &= f(3) = 6.5 \\
f[x_0, x_1] &= \frac{f(4) - f(3)}{4 - 3} = \frac{2 - 6.5}{1} = -4.5
\end{align}

\textbf{Newton's polynomial:}
\[
P_1(x) = 6.5 - 4.5(x - 3)
\]

\textbf{Evaluation at $x = 3.4$:}
\[
f(3.4) = 6.5 - 4.5(3.4 - 3) = 6.5 - 4.5(0.4) = 6.5 - 1.8 = \boxed{4.7}
\]

\textbf{Order 2 Interpolation: Points (2.5, 3, 4)}

Using points $(2.5, 7)$, $(3, 6.5)$, and $(4, 2)$:

\textbf{Divided differences:}
\begin{align}
f[x_0] &= 7, \quad f[x_1] = 6.5, \quad f[x_2] = 2 \\
f[x_0, x_1] &= \frac{6.5 - 7}{3 - 2.5} = \frac{-0.5}{0.5} = -1 \\
f[x_1, x_2] &= \frac{2 - 6.5}{4 - 3} = \frac{-4.5}{1} = -4.5 \\
f[x_0, x_1, x_2] &= \frac{f[x_1, x_2] - f[x_0, x_1]}{x_2 - x_0} = \frac{-4.5 - (-1)}{4 - 2.5} = \frac{-3.5}{1.5} = -2.333
\end{align}

\textbf{Newton's polynomial:}
\[
P_2(x) = 7 - 1(x - 2.5) - 2.333(x - 2.5)(x - 3)
\]

\textbf{Evaluation at $x = 3.4$:}
\begin{align}
f(3.4) &= 7 - 1(3.4 - 2.5) - 2.333(3.4 - 2.5)(3.4 - 3) \\
&= 7 - 1(0.9) - 2.333(0.9)(0.4) \\
&= 7 - 0.9 - 0.8398 \\
&= \boxed{5.260}
\end{align}

\textbf{Order 3 Interpolation: Points (2.5, 3, 4, 5)}

Using points $(2.5, 7)$, $(3, 6.5)$, $(4, 2)$, and $(5, 0)$:

\textbf{Additional divided differences:}
\begin{align}
f[x_2, x_3] &= \frac{0 - 2}{5 - 4} = -2 \\
f[x_1, x_2, x_3] &= \frac{f[x_2, x_3] - f[x_1, x_2]}{x_3 - x_1} = \frac{-2 - (-4.5)}{5 - 3} = \frac{2.5}{2} = 1.25 \\
f[x_0, x_1, x_2, x_3] &= \frac{f[x_1, x_2, x_3] - f[x_0, x_1, x_2]}{x_3 - x_0} = \frac{1.25 - (-2.333)}{5 - 2.5} = \frac{3.583}{2.5} = 1.433
\end{align}

\textbf{Newton's polynomial:}
\[
P_3(x) = 7 - 1(x - 2.5) - 2.333(x - 2.5)(x - 3) + 1.433(x - 2.5)(x - 3)(x - 4)
\]

\textbf{Evaluation at $x = 3.4$:}
\begin{align}
f(3.4) &= 7 - 0.9 - 0.8398 + 1.433(0.9)(0.4)(-0.6) \\
&= 6.1602 - 0.3097 \\
&= \boxed{4.950}
\end{align}

\subsubsection*{(II) Lagrange Interpolation Method}

Lagrange interpolation uses the formula:
\[
P_n(x) = \sum_{i=0}^{n} f(x_i) L_i(x)
\]
where $L_i(x) = \prod_{\substack{j=0 \\ j \neq i}}^{n} \frac{x - x_j}{x_i - x_j}$ are the Lagrange basis polynomials.

\textbf{Order 1 Interpolation: Points (3, 4)}

\textbf{Lagrange basis polynomials:}
\begin{align}
L_0(x) &= \frac{x - 4}{3 - 4} = \frac{x - 4}{-1} = -(x - 4) \\
L_1(x) &= \frac{x - 3}{4 - 3} = x - 3
\end{align}

\textbf{Interpolating polynomial:}
\[
P_1(x) = 6.5 \cdot L_0(x) + 2 \cdot L_1(x) = 6.5 \cdot (-(x-4)) + 2 \cdot (x-3)
\]

\textbf{Evaluation at $x = 3.4$:}
\begin{align}
f(3.4) &= 6.5 \cdot \frac{3.4 - 4}{3 - 4} + 2 \cdot \frac{3.4 - 3}{4 - 3} \\
&= 6.5 \cdot \frac{-0.6}{-1} + 2 \cdot \frac{0.4}{1} \\
&= 6.5 \cdot 0.6 + 2 \cdot 0.4 \\
&= 3.9 + 0.8 = \boxed{4.7}
\end{align}

\textbf{Order 2 Interpolation: Points (2.5, 3, 4)}

\textbf{Lagrange basis polynomials:}
\begin{align}
L_0(x) &= \frac{(x - 3)(x - 4)}{(2.5 - 3)(2.5 - 4)} = \frac{(x - 3)(x - 4)}{(-0.5)(-1.5)} = \frac{(x - 3)(x - 4)}{0.75} \\
L_1(x) &= \frac{(x - 2.5)(x - 4)}{(3 - 2.5)(3 - 4)} = \frac{(x - 2.5)(x - 4)}{(0.5)(-1)} = \frac{(x - 2.5)(x - 4)}{-0.5} \\
L_2(x) &= \frac{(x - 2.5)(x - 3)}{(4 - 2.5)(4 - 3)} = \frac{(x - 2.5)(x - 3)}{(1.5)(1)} = \frac{(x - 2.5)(x - 3)}{1.5}
\end{align}

\textbf{Evaluation at $x = 3.4$:}
\begin{align}
L_0(3.4) &= \frac{(3.4 - 3)(3.4 - 4)}{0.75} = \frac{(0.4)(-0.6)}{0.75} = \frac{-0.24}{0.75} = -0.32 \\
L_1(3.4) &= \frac{(3.4 - 2.5)(3.4 - 4)}{-0.5} = \frac{(0.9)(-0.6)}{-0.5} = \frac{-0.54}{-0.5} = 1.08 \\
L_2(3.4) &= \frac{(3.4 - 2.5)(3.4 - 3)}{1.5} = \frac{(0.9)(0.4)}{1.5} = \frac{0.36}{1.5} = 0.24
\end{align}

\textbf{Final calculation:}
\begin{align}
f(3.4) &= 7 \cdot (-0.32) + 6.5 \cdot (1.08) + 2 \cdot (0.24) \\
&= -2.24 + 7.02 + 0.48 \\
&= \boxed{5.260}
\end{align}

\textbf{Order 3 Interpolation: Points (2.5, 3, 4, 5)}

\textbf{Lagrange basis polynomials at $x = 3.4$:}
\begin{align}
L_0(3.4) &= \frac{(3.4 - 3)(3.4 - 4)(3.4 - 5)}{(2.5 - 3)(2.5 - 4)(2.5 - 5)} = \frac{(0.4)(-0.6)(-1.6)}{(-0.5)(-1.5)(-2.5)} = \frac{0.384}{-1.875} = -0.2048 \\
L_1(3.4) &= \frac{(3.4 - 2.5)(3.4 - 4)(3.4 - 5)}{(3 - 2.5)(3 - 4)(3 - 5)} = \frac{(0.9)(-0.6)(-1.6)}{(0.5)(-1)(-2)} = \frac{0.864}{1} = 0.864 \\
L_2(3.4) &= \frac{(3.4 - 2.5)(3.4 - 3)(3.4 - 5)}{(4 - 2.5)(4 - 3)(4 - 5)} = \frac{(0.9)(0.4)(-1.6)}{(1.5)(1)(-1)} = \frac{-0.576}{-1.5} = 0.384 \\
L_3(3.4) &= \frac{(3.4 - 2.5)(3.4 - 3)(3.4 - 4)}{(5 - 2.5)(5 - 3)(5 - 4)} = \frac{(0.9)(0.4)(-0.6)}{(2.5)(2)(1)} = \frac{-0.216}{5} = -0.0432
\end{align}

\textbf{Final calculation:}
\begin{align}
f(3.4) &= 7(-0.2048) + 6.5(0.864) + 2(0.384) + 0(-0.0432) \\
&= -1.4336 + 5.616 + 0.768 + 0 \\
&= \boxed{4.950}
\end{align}

\subsection*{(c) Summary and Comparison}

\textbf{Results Summary:}

\begin{center}
\renewcommand{\arraystretch}{1.3}
\begin{tabular}{|>{\centering\arraybackslash}p{3cm}|>{\centering\arraybackslash}p{2.5cm}|>{\centering\arraybackslash}p{2.5cm}|>{\centering\arraybackslash}p{2.5cm}|}
\hline
\rowcolor{blue!20}
\textbf{Method} & \textbf{Order 1} & \textbf{Order 2} & \textbf{Order 3} \\
\hline
Newton's Interpolation & \textbf{4.700} & \textbf{5.260} & \textbf{4.950} \\
\hline
Lagrange Interpolation & \textbf{4.700} & \textbf{5.260} & \textbf{4.950} \\
\hline
\rowcolor{green!20}
\textbf{Points Used} & (3, 4) & (2.5, 3, 4) & (2.5, 3, 4, 5) \\
\hline
\end{tabular}
\end{center}

\textbf{Key Observations:}

\begin{itemize}
    \item \textbf{Method Equivalence:} Both Newton's and Lagrange methods yield identical results for the same order of interpolation, confirming the mathematical equivalence of these approaches.

    \item \textbf{Convergence Pattern:} The interpolated values show variation with increasing order:
    \begin{itemize}
        \item Order 1: $f(3.4) = 4.700$ (linear interpolation)
        \item Order 2: $f(3.4) = 5.260$ (quadratic interpolation)
        \item Order 3: $f(3.4) = 4.950$ (cubic interpolation)
    \end{itemize}

    \item \textbf{Accuracy Considerations:} Higher-order interpolation doesn't always guarantee better accuracy. The choice of interpolation points and the underlying function behavior significantly affect the result.

    \item \textbf{Computational Efficiency:}
    \begin{itemize}
        \item Newton's method is more efficient for adding additional points (incremental construction)
        \item Lagrange method requires complete recalculation when adding new points
        \item For this specific problem, both methods require similar computational effort
    \end{itemize}
\end{itemize}

\textbf{Practical Recommendations:}

\begin{itemize}
    \item For this dataset, the cubic interpolation (Order 3) with $f(3.4) = 4.950$ appears most reasonable given the smooth variation in the data
    \item When choosing interpolation order, consider the trade-off between accuracy and computational complexity
    \item Always validate interpolation results by checking for physical reasonableness and consistency with known function behavior
    \item Consider using spline interpolation for larger datasets to avoid oscillatory behavior of high-order polynomials
\end{itemize}

\newpage

\section{Root Finding for a Spherical Water Tank}

\subsection*{(a) Determining Water Depth for Given Volume}

\textbf{Problem Setup:}

Given the volume of water in a spherical tank as:
\[
V = \pi h^2 \left( \frac{3R - h}{3} \right)
\]
where:
\begin{itemize}
    \item $V = 30 \, \text{m}^3$ (target volume)
    \item $R = 3 \, \text{m}$ (radius of the tank)
    \item $h$ is the unknown depth of water
\end{itemize}

\textbf{Mathematical Formulation:}

Substituting $R = 3$:
\[
V(h) = \pi h^2 \left( \frac{9 - h}{3} \right) = \frac{\pi h^2 (9 - h)}{3}
\]

Setting $V(h) = 30$, we define the root-finding function:
\[
f(h) = \frac{\pi h^2 (9 - h)}{3} - 30 = 0
\]

\subsubsection*{(i) Method Selection: Newton-Raphson}

We choose the \textbf{Newton-Raphson method} for the following reasons:
\begin{itemize}
    \item \textbf{Quadratic convergence:} Faster than bracketing methods when a good initial guess is available
    \item \textbf{Function properties:} The function is continuous and differentiable in the physical domain
    \item \textbf{Physical constraints:} The root is expected to lie between 0 and 6 (tank diameter)
    \item \textbf{Efficiency:} Requires fewer iterations compared to bisection method
\end{itemize}

\textbf{Derivative calculation:}
\[
f(h) = \frac{\pi h^2 (9 - h)}{3} - 30 = \frac{\pi}{3}(9h^2 - h^3) - 30
\]
\[
f'(h) = \frac{\pi}{3}(18h - 3h^2) = \frac{\pi h}{3}(18 - 3h) = \pi h(6 - h)
\]

\textbf{Newton-Raphson iteration formula:}
\[
h_{n+1} = h_n - \frac{f(h_n)}{f'(h_n)} = h_n - \frac{\frac{\pi h_n^2 (9 - h_n)}{3} - 30}{\pi h_n(6 - h_n)}
\]

\subsubsection*{(ii) Iterative Solution}

\textbf{Initial guess:} $h_0 = 2.0$ (reasonable estimate based on physical intuition)

\textbf{Iteration 1:}
\begin{align}
f(h_0) &= \frac{\pi (2)^2 (9 - 2)}{3} - 30 = \frac{28\pi}{3} - 30 \\
&= \frac{28 \times 3.14159}{3} - 30 = 29.322 - 30 = -0.678
\end{align}

\begin{align}
f'(h_0) &= \pi (2)(6 - 2) = 8\pi = 25.133
\end{align}

\begin{align}
h_1 &= 2.0 - \frac{-0.678}{25.133} = 2.0 + 0.0270 = 2.027
\end{align}

\textbf{Relative error:} $\varepsilon_1 = \left| \frac{2.027 - 2.0}{2.027} \right| = 0.0133 = 1.33\%$

\textbf{Iteration 2:}
\begin{align}
f(h_1) &= \frac{\pi (2.027)^2 (9 - 2.027)}{3} - 30 \\
&= \frac{\pi \times 4.109 \times 6.973}{3} - 30 = 30.030 - 30 = 0.030
\end{align}

\begin{align}
f'(h_1) &= \pi (2.027)(6 - 2.027) = \pi \times 2.027 \times 3.973 = 25.301
\end{align}

\begin{align}
h_2 &= 2.027 - \frac{0.030}{25.301} = 2.027 - 0.0012 = 2.0258
\end{align}

\textbf{Relative error:} $\varepsilon_2 = \left| \frac{2.0258 - 2.027}{2.0258} \right| = 0.00059 = 0.059\%$

\textbf{Iteration 3:}
\begin{align}
f(h_2) &= \frac{\pi (2.0258)^2 (9 - 2.0258)}{3} - 30 \\
&\approx 30.0001 - 30 = 0.0001
\end{align}

\begin{align}
f'(h_2) &= \pi (2.0258)(6 - 2.0258) = 25.295
\end{align}

\begin{align}
h_3 &= 2.0258 - \frac{0.0001}{25.295} = 2.0258 - 0.000004 = 2.02576
\end{align}

\textbf{Relative error:} $\varepsilon_3 = \left| \frac{2.02576 - 2.0258}{2.02576} \right| = 0.000002 = 0.0002\%$

\subsubsection*{(iii) Convergence Analysis}

\begin{center}
\renewcommand{\arraystretch}{1.3}
\begin{tabular}{|c|c|c|c|}
\hline
\rowcolor{blue!20}
\textbf{Iteration} & \textbf{$h_n$ (m)} & \textbf{$f(h_n)$} & \textbf{Relative Error (\%)} \\
\hline
0 & 2.000 & -0.678 & - \\
\hline
1 & 2.027 & 0.030 & 1.33 \\
\hline
2 & 2.0258 & 0.0001 & 0.059 \\
\hline
3 & 2.02576 & $\approx 0$ & 0.0002 \\
\hline
\end{tabular}
\end{center}

\textbf{Final Answer:} The depth of water should be approximately $\boxed{h = 2.026 \text{ m}}$ to achieve a volume of 30 m³.

\textbf{Verification:}
\[
V = \frac{\pi (2.026)^2 (9 - 2.026)}{3} = \frac{\pi \times 4.105 \times 6.974}{3} = 30.00 \text{ m}^3 \checkmark
\]

\subsection*{(b) Matrix Equation Solution Conditions}

For a linear system $A \cdot X = B$, where $A$ is an $m \times n$ matrix, $X$ is an $n \times 1$ vector, and $B$ is an $m \times 1$ vector, the existence and uniqueness of solutions depend on the ranks of the coefficient matrix and the augmented matrix.

\subsubsection*{Mathematical Foundation}

Let:
\begin{itemize}
    \item $\text{rank}(A) = r_A$ = rank of coefficient matrix
    \item $\text{rank}([A|B]) = r_{AB}$ = rank of augmented matrix $[A|B]$
    \item $n$ = number of unknowns (columns in $A$)
    \item $m$ = number of equations (rows in $A$)
\end{itemize}

\subsubsection*{Solution Classification}

\textbf{Case 1: Unique Solution}
\begin{itemize}
    \item \textbf{Condition:} $\text{rank}(A) = \text{rank}([A|B]) = n$
    \item \textbf{Requirements:}
    \begin{itemize}
        \item The system is consistent (no contradictory equations)
        \item Matrix $A$ has full column rank
        \item For square matrices: $\det(A) \neq 0$
    \end{itemize}
    \item \textbf{Interpretation:} There are exactly $n$ linearly independent equations for $n$ unknowns
    \item \textbf{Example:} $A = \begin{bmatrix} 2 & 1 \\ 1 & 3 \end{bmatrix}$, $B = \begin{bmatrix} 5 \\ 7 \end{bmatrix}$ has unique solution
\end{itemize}

\textbf{Case 2: No Solution (Inconsistent System)}
\begin{itemize}
    \item \textbf{Condition:} $\text{rank}(A) \neq \text{rank}([A|B])$
    \item \textbf{Equivalently:} $\text{rank}(A) < \text{rank}([A|B])$
    \item \textbf{Interpretation:} The vector $B$ is not in the column space of $A$
    \item \textbf{Physical meaning:} The equations are contradictory
    \item \textbf{Example:} $\begin{bmatrix} 1 & 2 \\ 2 & 4 \end{bmatrix} \begin{bmatrix} x_1 \\ x_2 \end{bmatrix} = \begin{bmatrix} 3 \\ 7 \end{bmatrix}$ (second equation gives $x_1 + 2x_2 = 3.5$, contradicting the first)
\end{itemize}

\textbf{Case 3: Infinitely Many Solutions}
\begin{itemize}
    \item \textbf{Condition:} $\text{rank}(A) = \text{rank}([A|B]) < n$
    \item \textbf{Requirements:}
    \begin{itemize}
        \item The system is consistent
        \item There are fewer linearly independent equations than unknowns
    \end{itemize}
    \item \textbf{Solution structure:} $X = X_p + X_h$, where:
    \begin{itemize}
        \item $X_p$ is a particular solution
        \item $X_h$ is the general solution to the homogeneous system $AX = 0$
    \end{itemize}
    \item \textbf{Free variables:} Number of free variables = $n - \text{rank}(A)$
    \item \textbf{Example:} $\begin{bmatrix} 1 & 2 & 3 \\ 2 & 4 & 6 \end{bmatrix} \begin{bmatrix} x_1 \\ x_2 \\ x_3 \end{bmatrix} = \begin{bmatrix} 5 \\ 10 \end{bmatrix}$ has infinitely many solutions
\end{itemize}

\subsubsection*{Practical Determination Methods}

\textbf{For Square Matrices ($m = n$):}
\begin{itemize}
    \item Calculate $\det(A)$:
    \begin{itemize}
        \item If $\det(A) \neq 0$: Unique solution exists
        \item If $\det(A) = 0$: Either no solution or infinitely many solutions
    \end{itemize}
    \item Use Gaussian elimination to determine consistency
\end{itemize}

\textbf{For General Matrices:}
\begin{itemize}
    \item Perform row reduction on $[A|B]$ to row echelon form
    \item Count the number of pivot columns in $A$ and $[A|B]$
    \item Apply the rank conditions above
\end{itemize}

\subsubsection*{Summary Table}

\begin{center}
\renewcommand{\arraystretch}{1.4}
\begin{tabular}{|>{\centering\arraybackslash}p{3cm}|>{\centering\arraybackslash}p{3.5cm}|>{\centering\arraybackslash}p{6cm}|}
\hline
\rowcolor{blue!20}
\textbf{Solution Type} & \textbf{Rank Condition} & \textbf{Characteristics} \\
\hline
\textbf{Unique} & $\text{rank}(A) = \text{rank}([A|B]) = n$ & System is consistent and determined \\
\hline
\textbf{No Solution} & $\text{rank}(A) < \text{rank}([A|B])$ & System is inconsistent (contradictory) \\
\hline
\textbf{Infinite Solutions} & $\text{rank}(A) = \text{rank}([A|B]) < n$ & System is consistent but underdetermined \\
\hline
\end{tabular}
\end{center}

\end{document}
